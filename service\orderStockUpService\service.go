package orderStockUpService

import (
	"base/core/xerr"
	"base/dao"
	"base/dao/orderStockUpDao"
	"base/global"
	"base/model"
	"base/payModule"
	"base/service/authenticationService"
	"base/service/buyerService"
	"base/service/miniService"
	"base/service/orderService"
	"base/service/orderStatusRecordService"
	"base/service/payAccountService"
	"base/service/payOrderService"
	"base/service/productCommissionService"
	"base/service/productImageService"
	"base/service/productService"
	"base/service/routeService"
	"base/service/servicePointCommissionService"
	"base/service/servicePointService"
	"base/service/supplierService"
	"base/service/userAddrService"
	"base/service/warehouseService"
	"context"
	"time"

	_ "github.com/alibabacloud-go/ecs-********/v2/client"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

// ServiceInterface 备货
type ServiceInterface interface {
	AddOrderToStockUp(ctx context.Context, orderID primitive.ObjectID, supplier model.Supplier, timestamp int64) error // 加入备货组
	UpdateStockUp(ctx context.Context, id primitive.ObjectID, stockNum int) error                                      // 备货
	UpdateOne(ctx context.Context, filter, update bson.M) error
	UpdateMany(ctx context.Context, filter, update bson.M) error
	GetStockUp(ctx context.Context, id primitive.ObjectID) (model.OrderStockUp, error)
	ListStockOrder(ctx context.Context, supplierID, servicePointID primitive.ObjectID, timestamp int64, stockUpHas bool) ([]model.OrderStockUp, error)
	List(ctx context.Context, filter bson.M) ([]model.OrderStockUp, error)
	ListStockOrderToDo(ctx context.Context, supplierID primitive.ObjectID, timestamp int64) ([]model.StockUpToDo, error)
	ListStockOrderToDoAll(ctx context.Context, servicePointID primitive.ObjectID) ([]model.Order, error)
	CountNotConfirm(ctx context.Context, servicePointID primitive.ObjectID) (int64, error)
	ListByWarehouseAndPoint(ctx context.Context, warehouseID, pointID primitive.ObjectID, timestamp int64) ([]model.OrderStockUp, error)
	ListByWarehouse(ctx context.Context, warehouseID primitive.ObjectID, timestamp int64, hasQuality bool) ([]model.OrderStockUp, error)
	ListByServicePoint(ctx context.Context, pointID primitive.ObjectID, timestamp int64) ([]model.OrderStockUp, error)
	ListByNo(ctx context.Context, warehouseID primitive.ObjectID, timestamp int64, hasSort bool) ([]model.OrderStockUp, error)
	ListBySort(ctx context.Context, warehouseID primitive.ObjectID, timestamp int64) ([]model.OrderStockUp, error)
}
type orderStockUpService struct {
	mdb                *mongo.Database
	rdb                *redis.Client
	l                  *zap.SugaredLogger
	orderS             orderService.ServiceInterface
	orderStockUpDao    orderStockUpDao.DaoInt
	orderStatusRecordS orderStatusRecordService.ServiceInterface
	buyerS             buyerService.ServiceInterface

	productS                productService.ServiceInterface
	routeFeeS               routeService.ServiceInterface
	servicePointS           servicePointService.ServiceInterface
	warehouseS              warehouseService.ServiceInterface
	servicePointCommissionS servicePointCommissionService.ServiceInterface
	productImageS           productImageService.ServiceInterface
	productCommissionS      productCommissionService.ServiceInterface
	//供应商
	supplierS supplierService.ServiceInterface
	// 地址
	userAddrS userAddrService.ServiceInterface

	authenticationS authenticationService.ServiceInterface

	mini miniService.ServiceInterface

	//	费率
	// 支付公司
	PayCompanyCommission float64

	// 平台营销账户余额
	payAccountS payAccountService.ServiceInterface

	allInPayOrderS payModule.OrderService
	// 支付单
	payOrderS payOrderService.ServiceInterface
}

// NewOrderStockUpService 创建备货订单服务
func NewOrderStockUpService() ServiceInterface {
	return orderStockUpService{
		mdb:                     global.MDB,
		rdb:                     global.RDBDefault,
		l:                       global.OrderLogger.Sugar(),
		buyerS:                  buyerService.NewBuyerService(),
		orderS:                  orderService.NewOrderService(),
		orderStockUpDao:         dao.OrderStockUpDao,
		orderStatusRecordS:      orderStatusRecordService.NewOrderStatusRecordService(),
		productS:                productService.NewProductService(),
		routeFeeS:               routeService.NewTransportFeeService(),
		servicePointS:           servicePointService.NewServicePointService(),
		warehouseS:              warehouseService.NewWarehouseServiceService(),
		servicePointCommissionS: servicePointCommissionService.NewPartnerCommissionService(),
		productImageS:           productImageService.NewProductImageService(),
		productCommissionS:      productCommissionService.NewProductCommissionService(),
		supplierS:               supplierService.NewSupplierService(),
		userAddrS:               userAddrService.NewUserAddrService(),

		mini: miniService.NewMiniService(),

		// 平台
		payAccountS: payAccountService.NewPayAccountService(),

		allInPayOrderS: payModule.NewOrderS(),
		payOrderS:      payOrderService.NewPayOrderService(),
	}
}

// ListStockOrder 备货列表
func (s orderStockUpService) ListStockOrder(ctx context.Context, supplierID, servicePointID primitive.ObjectID, timestamp int64, stockUpHas bool) ([]model.OrderStockUp, error) {
	filter := bson.M{
		"supplier_id": supplierID,
		//"service_point_id": servicePointID,
		"no":           timestamp,
		"stock_up_has": stockUpHas,
	}

	ups, err := s.orderStockUpDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return ups, nil
}

func (s orderStockUpService) UpdateOne(ctx context.Context, filter, update bson.M) error {
	err := s.orderStockUpDao.UpdateOne(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s orderStockUpService) UpdateMany(ctx context.Context, filter, update bson.M) error {
	err := s.orderStockUpDao.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	return nil
}

func (s orderStockUpService) AddOrderToStockUp(ctx context.Context, orderID primitive.ObjectID, supplier model.Supplier, timestamp int64) error {
	// 查询已备货
	ups, err := s.ListStockOrderBy(ctx, supplier.ID, timestamp)
	if err != nil {
		return err
	}
	mUp := make(map[primitive.ObjectID]model.OrderStockUp)
	for _, up := range ups {
		for _, order := range up.OrderList {
			if order.OrderID == orderID {
				return xerr.NewErr(xerr.ErrParamError, nil, "该订单已在备货单中")
			}
		}
		mUp[up.ProductID] = up
	}

	order, err := s.orderS.Get(ctx, orderID)
	if err != nil {
		return err
	}

	buyer, err := s.buyerS.Get(order.BuyerID)
	if err != nil {
		return err
	}
	now := time.Now().UnixMilli()
	//session, err := s.mdb.Client().StartSession()
	//if err != nil {
	//	return err
	//}
	//defer session.EndSession(ctx)
	//_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
	for _, p := range order.ProductList {
		dw := p.Num * p.RoughWeight
		sortW := 0
		if !p.IsCheckWeight {
			sortW = dw
		}

		if v, ok := mUp[p.ProductID]; ok {
			//	 已有相同产品备货
			num := v.StockUpDueNum + p.Num
			ql := v.QualityServicePointList // 服务点列表

			var f bool
			for i, point := range v.QualityServicePointList {
				if order.ServicePointID == point.ServicePointID {
					f = true
					point.QualityDueNum += p.Num
					ql[i] = point
				}
			}
			if !f {
				// 不存在
				ql = append(ql, model.PerServicePoint{
					ServicePointID: order.ServicePointID,
					QualityDueNum:  p.Num,
				})
			}

			d := model.PerOrder{
				OrderID:          orderID,
				BuyerId:          order.BuyerID,
				BuyerName:        buyer.BuyerName,
				Address:          order.Address,
				WarehouseID:      order.WarehouseID,
				WarehouseName:    order.WarehouseName,
				ServicePointID:   order.ServicePointID,
				ServicePointName: order.ServicePointName,
				DueNum:           p.Num,
				DueWeight:        dw,
				SortNum:          0,
				SortWeight:       sortW,
			}

			v.OrderList = append(v.OrderList, d)

			update := bson.M{
				"stock_up_due_num":           num,
				"quality_service_point_list": ql,
				"order_list":                 v.OrderList,
			}
			err = s.orderStockUpDao.UpdateOne(ctx, bson.M{"_id": v.ID}, bson.M{"$set": update})
			if err != nil {
				//return nil, err
				return err
			}
		} else {
			ql := make([]model.PerServicePoint, 0)
			ql = append(ql, model.PerServicePoint{
				ServicePointID:   order.ServicePointID,
				ServicePointName: order.ServicePointName,
				QualityDueNum:    p.Num,
				QualityNum:       0,
			})

			//	新建
			data := model.OrderStockUp{
				ID:                      primitive.NewObjectID(),
				SupplierID:              supplier.ID,
				SupplierName:            supplier.ShopSimpleName,
				QualityServicePointList: ql,
				ProductID:               p.ProductID,
				PerRoughWeight:          p.RoughWeight,
				IsCheckWeight:           p.IsCheckWeight,
				HasParam:                p.HasParam,
				ProductParamType:        p.ProductParamType,
				StandardAttr:            p.StandardAttr,
				NonStandardAttr:         p.NonStandardAttr,
				CategoryIDs:             p.CategoryIDs,
				ProductTitle:            p.ProductTitle,
				ProductCover:            p.ProductCoverImg,
				OrderList: []model.PerOrder{{
					OrderID:          orderID,
					BuyerId:          order.BuyerID,
					BuyerName:        buyer.BuyerName,
					Address:          order.Address,
					WarehouseID:      order.WarehouseID,
					WarehouseName:    order.WarehouseName,
					ServicePointID:   order.ServicePointID,
					ServicePointName: order.ServicePointName,
					DueNum:           p.Num,
					DueWeight:        dw,
					SortNum:          0,
					SortWeight:       sortW,
				}},
				StockUpDueNum: p.Num,
				No:            timestamp,
				CreatedAt:     time.Now().UnixMilli(),
			}
			err = s.orderStockUpDao.Create(ctx, data)
			if err != nil {
				return err
			}
		}
	}
	filter := bson.M{
		"_id": orderID,
		"order_status": bson.M{
			"$lte": model.OrderStatusTypeToQuality, // 小于品控环节的
		},
	}
	err = s.orderS.UpdateOne(ctx, filter, bson.M{"$set": bson.M{
		"order_status": model.OrderStatusTypeToQuality, // 待品控
	}})
	if err != nil {
		return err
		//return nil, err
	}

	status := model.OrderStatusTypeToQuality
	key := model.BackRecordKey(status)

	err = s.orderS.UpdateMany(ctx, filter, bson.M{"$set": bson.M{
		"order_status":               status, // 待品控
		"order_status_record." + key: now,    // 状态记录
	}})
	if err != nil {
		return err
	}
	return nil
}

func (s orderStockUpService) ListStockOrderBy(ctx context.Context, supplierID primitive.ObjectID, timestamp int64) ([]model.OrderStockUp, error) {
	filter := bson.M{
		"supplier_id": supplierID,
		"no":          timestamp,
	}

	ups, err := s.orderStockUpDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return ups, nil
}

func (s orderStockUpService) ListByWarehouseAndPoint(ctx context.Context, warehouseID, pointID primitive.ObjectID, timestamp int64) ([]model.OrderStockUp, error) {
	filter := bson.M{
		"order_list.warehouse_id": warehouseID,
		"no":                      timestamp,
	}
	if pointID != primitive.NilObjectID {
		filter["service_point_id"] = pointID
	}
	ups, err := s.orderStockUpDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return ups, nil
}

func (s orderStockUpService) ListByWarehouse(ctx context.Context, warehouseID primitive.ObjectID, timestamp int64, hasQuality bool) ([]model.OrderStockUp, error) {
	filter := bson.M{
		"order_list.warehouse_id": warehouseID,
		"no":                      timestamp,
		"quality_has":             hasQuality,
	}
	ups, err := s.orderStockUpDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return ups, nil
}

func (s orderStockUpService) ListByServicePoint(ctx context.Context, pointID primitive.ObjectID, timestamp int64) ([]model.OrderStockUp, error) {
	filter := bson.M{
		"service_point_id": pointID,
		"no":               timestamp,
	}
	ups, err := s.orderStockUpDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return ups, nil
}

func (s orderStockUpService) ListBySort(ctx context.Context, warehouseID primitive.ObjectID, timestamp int64) ([]model.OrderStockUp, error) {
	filter := bson.M{
		"order_list.warehouse_id": warehouseID,
		//"sort_has":                hasSort,
		"no": timestamp,
	}
	ups, err := s.orderStockUpDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return ups, nil
}

func (s orderStockUpService) ListByNo(ctx context.Context, warehouseID primitive.ObjectID, timestamp int64, hasSort bool) ([]model.OrderStockUp, error) {
	filter := bson.M{
		"warehouse_id": warehouseID,
		//"sort_has":     hasSort,
		"no": timestamp,
	}
	ups, err := s.orderStockUpDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return ups, nil
}

func (s orderStockUpService) ListStockOrderToDo(ctx context.Context, supplierID primitive.ObjectID, timestamp int64) ([]model.StockUpToDo, error) {
	filter := bson.M{
		"supplier_id": supplierID,
		"pay_status":  model.PayStatusTypePaid, // 已支付
		"order_status": bson.M{
			"$gte": model.OrderStatusTypeToStockUp,
			"$lte": model.OrderStatusTypeToShip,
		}, // 待发货
	}

	ups, err := s.ListStockOrderBy(ctx, supplierID, timestamp)
	if err != nil {
		return nil, err
	}
	var orderIDs []primitive.ObjectID
	for _, up := range ups {
		for _, order := range up.OrderList {
			orderIDs = append(orderIDs, order.OrderID)
		}
	}
	if len(orderIDs) > 0 {
		filter["_id"] = bson.M{
			"$nin": orderIDs,
		}
	}

	orders, err := s.orderS.List(ctx, bson.M{
		"supplier_id":  supplierID,
		"pay_status":   model.PayStatusTypePaid,        // 已支付
		"order_status": model.OrderStatusTypeToStockUp, // 待备货
	},
	)
	if err != nil {
		return nil, err
	}

	var orderToDo []model.StockUpToDo
	for _, order := range orders {
		var pList []model.PerProduct
		for _, p := range order.ProductList {
			//	付款后，备货前未退款
			temp := model.PerProduct{
				ProductID:       p.ProductID,
				ProductTitle:    p.ProductTitle,
				ProductCover:    p.ProductCoverImg,
				StandardAttr:    p.StandardAttr,
				NonStandardAttr: p.NonStandardAttr,
				Num:             p.Num,
			}
			pList = append(pList, temp)
		}

		point, err := s.servicePointS.Get(ctx, order.ServicePointID)
		if err != nil {
			return nil, err
		}

		orderToDo = append(orderToDo, model.StockUpToDo{
			OrderID:          order.ID,
			Address:          order.Address,
			ServicePointID:   order.ServicePointID,
			ServicePointName: point.Name,
			OrderCreateTime:  order.CreatedAt,
			ProductList:      pList,
		})
	}

	return orderToDo, nil
}

func (s orderStockUpService) ListStockOrderToDoAll(ctx context.Context, servicePointID primitive.ObjectID) ([]model.Order, error) {
	filter := bson.M{
		"service_point_id": servicePointID,
		"pay_status":       model.PayStatusTypePaid,        // 已支付
		"order_status":     model.OrderStatusTypeToStockUp, // 待备货
		"order_type":       model.OrderTypeWholeSale,
	}

	orders, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return orders, nil
}

func (s orderStockUpService) CountNotConfirm(ctx context.Context, servicePointID primitive.ObjectID) (int64, error) {
	filter := bson.M{
		"service_point_id": servicePointID,
		"pay_status":       model.PayStatusTypePaid,        // 已支付
		"order_status":     model.OrderStatusTypeToStockUp, // 待备货
		"order_type":       model.OrderTypeWholeSale,
		"user_type":        model.UserTypeNormal,
	}

	count, err := s.orderS.Count(ctx, filter)
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (s orderStockUpService) List(ctx context.Context, filter bson.M) ([]model.OrderStockUp, error) {
	ups, err := s.orderStockUpDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}

	return ups, nil
}

func (s orderStockUpService) GetStockUp(ctx context.Context, id primitive.ObjectID) (model.OrderStockUp, error) {
	up, err := s.orderStockUpDao.Get(ctx, bson.M{"_id": id})
	if err != nil {
		return model.OrderStockUp{}, err
	}
	return up, nil
}

// ListNotStockUpOrder 未备货订单
func (s orderStockUpService) ListNotStockUpOrder(ctx context.Context, supplierID, servicePointID primitive.ObjectID, timestamp int64) ([]model.StockUpInfo, error) {
	//	timeStart, timeEnd, page, limit int64
	filter := bson.M{
		"supplier_id": supplierID,
		//"service_point_id": servicePointID,
		"pay_status": model.PayStatusTypePaid, // 已支付
		"order_status": bson.M{
			"$gte": model.OrderStatusTypeToStockUp,
			"$lte": model.OrderStatusTypeToShip,
		}, // 待发货
		//"$and": bson.A{
		//	bson.M{"created_at": bson.M{"$gt": timeStart}},
		//	bson.M{"created_at": bson.M{"$lt": timeEnd}},
		//},
	}
	orders, err := s.orderS.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	mProduct := make(map[primitive.ObjectID]model.StockUpInfo)

	for _, order := range orders {
		for _, p := range order.ProductList {
			//	付款后，备货前未退款
			temp := mProduct[p.ProductID]
			temp.StockUpDueNum += p.Num           // 应有备货-累加
			temp.ProductID = p.ProductID          // 商品ID
			temp.ProductTitle = p.ProductTitle    // 商品标题
			temp.ProductCover = p.ProductCoverImg // 商品封面
			temp.OrderIDList = append(temp.OrderIDList, model.PerOrder{
				OrderID: order.ID,
				DueNum:  p.Num,
			})
			mProduct[p.ProductID] = temp
		}
	}
	var list []model.StockUpInfo
	for _, info := range mProduct {
		list = append(list, info)
	}

	return list, nil
}

// UpdateStockUp 备货
func (s orderStockUpService) UpdateStockUp(ctx context.Context, id primitive.ObjectID, stockNum int) error {
	now := time.Now().UnixMilli()
	up, err := s.GetStockUp(ctx, id)
	if err != nil {
		return err
	}
	if stockNum > up.StockUpDueNum {
		return xerr.NewErr(xerr.ErrParamError, nil, "备货数不可大于应备货数")
	}

	var hasQualityNum int
	for _, i := range up.QualityServicePointList {
		hasQualityNum += i.QualityNum
	}
	if stockNum < hasQualityNum {
		s.l.Errorf("请求：备货数%d，已品控数%d，", stockNum, hasQualityNum)
		return xerr.NewErr(xerr.ErrParamError, nil, "备货数不可小于已品控数")
	}

	var shipCount int
	for _, order := range up.OrderList {
		if order.HasShip {
			shipCount += order.DueNum - order.SortNum
		}
	}
	//	 已发货
	if stockNum > up.StockUpDueNum-shipCount {
		s.l.Errorf("应备货数：%d,已发货订单备货差额为%d,当次备货数%d", up.StockUpDueNum, shipCount, stockNum)
		return xerr.NewErr(xerr.ErrParamError, nil, "备货数不可大于应备货数")
	}

	session, err := s.mdb.Client().StartSession()
	if err != nil {
		return err
	}
	defer session.EndSession(ctx)
	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {

		err = s.orderStockUpDao.UpdateOne(sessCtx, bson.M{"_id": id}, bson.M{"$set": bson.M{
			"stock_up_has_num": stockNum,
			"quality_has":      false,
			"stock_up_has":     true,
			"updated_at":       now,
		}})
		if err != nil {
			return nil, err
		}
		var oIDs []primitive.ObjectID
		for _, order := range up.OrderList {
			oIDs = append(oIDs, order.OrderID)
		}

		filter := bson.M{
			"_id": bson.M{"$in": oIDs},
			"order_status": bson.M{
				"$lte": model.OrderStatusTypeToQuality, // 小于品控环节的
			},
		}
		status := model.OrderStatusTypeToQuality
		key := model.BackRecordKey(status)

		err = s.orderS.UpdateMany(sessCtx, filter, bson.M{"$set": bson.M{
			"order_status":               status, // 待品控
			"order_status_record." + key: now,    // 状态记录
		}})
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	if err != nil {
		return err
	}
	return nil
}
