package buyer

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/buyerService"
	"base/util"

	"github.com/gin-gonic/gin"
)

// InitPWD 初始化密码
func InitPWD(ctx *gin.Context) {
	var req = struct {
		Mobile string `json:"mobile" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if !util.CheckMobile(req.Mobile) {
		err = xerr.NewErr(xerr.ErrParamError, nil, "手机号格式错误")
		xhttp.RespErr(ctx, err)
		return
	}

	err = buyerService.NewBuyerService().InitPWD(ctx, req.Mobile)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}

// ResetPWD 重置密码
func ResetPWD(ctx *gin.Context) {
	var req = struct {
		Mobile  string `json:"mobile"`
		Captcha string `json:"captcha"`
		PWD     string `json:"pwd"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if !util.CheckMobile(req.Mobile) {
		err = xerr.NewErr(xerr.ErrParamError, nil, "手机号格式错误")
		xhttp.RespErr(ctx, err)
		return
	}

	err = buyerService.NewBuyerService().ResetPWD(ctx, req.Mobile, req.Captcha, req.PWD)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
