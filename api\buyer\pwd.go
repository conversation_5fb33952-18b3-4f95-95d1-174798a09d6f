package buyer

import (
	"base/core/xerr"
	"base/core/xhttp"
	"base/service/buyerService"
	"base/util"

	"github.com/gin-gonic/gin"
)

// ResetPWD 重置密码
func ResetPWD(ctx *gin.Context) {
	var req = struct {
		Mobile string `json:"mobile"`
		PWD    string `json:"pwd"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	if !util.CheckMobile(req.Mobile) {
		err = xerr.NewErr(xerr.ErrParamError, nil, "手机号格式错误")
		xhttp.RespErr(ctx, err)
		return
	}

	if req.PWD == "" {
		err = xerr.NewErr(xerr.ErrParamError, nil, "请输入密码")
		xhttp.RespErr(ctx, err)
		return
	}

	err = buyerService.NewBuyerService().ResetPWD(ctx, req.Mobile, "", req.PWD)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	xhttp.RespSuccess(ctx, nil)
}
