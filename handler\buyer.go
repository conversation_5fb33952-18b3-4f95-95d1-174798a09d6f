package handler

import (
	"base/api/buyer"
	"base/api/buyerGroup"
	"base/core/middleware"

	"github.com/gin-gonic/gin"
)

// 采购商
func buyerRouter(r *gin.RouterGroup) {
	r = r.Group("/buyer")

	r.POST("/login/by/wechat", buyer.LoginByWechat)
	r.POST("/login/by/mobile", buyer.LoginByMobile)
	r.POST("/login/send/captcha", buyer.SendLoginCaptcha)
	r.POST("/login/by/pwd", buyer.LoginByPWD)

	l := r.Use(middleware.CheckToken)
	l.POST("/pwd/reset", buyer.ResetPWD)

	l.POST("/", buyer.Apply)
	l.POST("/apply/update", buyer.UpdateApply)
	l.POST("/update", buyer.UpdateBuyer)
	l.POST("/avatar/update", buyer.UpdateAvatar)
	l.POST("/license/status/update", buyer.UpdateLicenseStatus)
	l.POST("/service/fee/type/update", buyer.UpdateServiceFee)
	l.POST("/user/type/update", buyer.UpdateUserType)
	l.POST("/user", buyer.GetByUser)
	l.POST("/search", buyer.Search)
	l.POST("/search/address", buyer.SearchByAddress)
	l.POST("/search/mobile", buyer.SearchByLoginMobile)
	l.POST("/search/mobile/v2", buyer.SearchByLoginMobileV2)
	l.POST("/list/latest/active", buyer.LatestActive)
	l.POST("/list/by/station", buyer.ListByStation)
	l.GET("/:id", buyer.Get)
	l.POST("/get", buyer.GetByPost)
	l.POST("/stats/get", buyer.GetBuyerStats)
	l.POST("/export", buyer.ExportBuyer)

	l.POST("/set/real/name", buyer.SetRealName)

	// 银行卡

	//	 交流群
	l.POST("/group/upsert", buyerGroup.Upsert)
	l.POST("/group/list", buyerGroup.List)
	l.POST("/group/list/all", buyerGroup.ListAll)
	l.POST("/group/delete", buyerGroup.Delete)
}
