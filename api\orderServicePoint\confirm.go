package orderServicePoint

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderStockUpService"
	"base/service/supplierService"
	"base/service/userService"
	"base/util"
	"sort"

	"github.com/gin-gonic/gin"
)

// 订单确认

// ListNotConfirmOrder  所有未确认订单
func ListNotConfirmOrder(ctx *gin.Context) {
	var req = struct {
		ServicePointID string `json:"service_point_id"`
		StationID      string `json:"station_id"`
		Timestamp      int64  `json:"timestamp"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	stockUpInfos, err := orderStockUpService.NewOrderStockUpService().ListStockOrderToDoAll(ctx, servicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var list []toStockRes
	for _, i := range stockUpInfos {
		supplier, _ := supplierService.NewSupplierService().Get(ctx, i.SupplierID)
		user, _ := userService.NewUserService().Get(ctx, supplier.UserID)

		list = append(list, toStockRes{
			Order:          i,
			SupplierMobile: user.Mobile,
		})
	}

	sort.Sort(toStockList(list))

	xhttp.RespSuccess(ctx, list)
}

type toStockRes struct {
	model.Order
	SupplierMobile string `json:"supplier_mobile"`
}
