package userService

import (
	"base/core/xerr"
	"base/dao/userDao"
	"base/global"
	"base/model"
	"base/service/aesService"
	"base/service/messageService"
	"base/service/miniService"
	"context"
	"errors"
	"time"

	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ServiceInterface interface {
	LoginByMobile(ctx context.Context, mobile, captcha, loginCode string) (model.User, error)
	LoginByPWD(ctx context.Context, mobile, pwd string) (model.User, error)
	ResetPWD(ctx context.Context, mobile, captcha, pwd string) error
	CreateUser(ctx context.Context, mobile, pwd string) (model.User, error)
	Login(ctx context.Context, loginCode, mobileCode string) (model.User, error)
	Get(ctx context.Context, id primitive.ObjectID) (model.User, error)
	GetByMobile(ctx context.Context, mobile string) (model.User, error)
	GetByOpenID(ctx context.Context, env model.ObjectType, openID string) (model.User, error)
	RemoveObjectType(ctx context.Context, uid primitive.ObjectID, objectType model.ObjectType) error
	GetOrCreateUser(ctx context.Context, mobile, openID, unionID string) (model.User, error)
	AdminCreateUser(ctx context.Context, mobile, note string) (model.User, error)
	List(ctx context.Context, page, limit int64) ([]model.User, int64, error)
	ListByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.User, error)
	ListByRegexMobile(ctx context.Context, mobile string, page, limit int64) ([]model.User, int64, error)
	UpdateMobile(ctx context.Context, id primitive.ObjectID, mobile string) error
}

type userService struct {
	mdb     *mongo.Database
	db      *mongo.Collection
	rdb     *redis.Client
	AesSvr  aesService.ServiceInterface
	userDao userDao.UserDao
	mini    miniService.ServiceInterface
}

func NewUserService() ServiceInterface {
	return userService{
		mdb:     global.MDB,
		db:      global.MDB.Collection("user"),
		rdb:     global.RDBDefault,
		AesSvr:  aesService.NewAesService(),
		userDao: userDao.NewUserDao(),
		mini:    miniService.NewMiniService(),
	}
}

func (s userService) List(ctx context.Context, page, limit int64) ([]model.User, int64, error) {
	filter := bson.M{}
	users, i, err := s.userDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return users, i, nil
}

func (s userService) ListByRegexMobile(ctx context.Context, mobile string, page, limit int64) ([]model.User, int64, error) {
	filter := bson.M{
		"mobile": bson.M{
			"$regex": mobile,
		},
	}
	users, i, err := s.userDao.ListByPage(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return users, i, nil
}

func (s userService) ListByIDs(ctx context.Context, ids []primitive.ObjectID) ([]model.User, error) {
	if len(ids) < 1 {
		return nil, nil
	}
	filter := bson.M{
		"_id": bson.M{
			"$in": ids,
		},
	}
	users, err := s.userDao.List(ctx, filter)
	if err != nil {
		return nil, err
	}
	return users, nil
}

func (s userService) AdminCreateUser(ctx context.Context, mobile, note string) (model.User, error) {
	user, err := s.GetByMobile(ctx, mobile)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return model.User{}, err
	}
	if user.ID != primitive.NilObjectID {
		return model.User{}, xerr.NewErr(xerr.ErrParamError, nil, "用户已存在")
	}
	u := model.User{
		ID:        primitive.NewObjectID(),
		Mobile:    mobile,
		Note:      note,
		CreatedAt: time.Now().UnixMilli(),
	}
	err = s.userDao.Create(ctx, u)
	if err != nil {
		return model.User{}, err
	}
	return u, nil
}

func (s userService) RemoveObjectType(ctx context.Context, uid primitive.ObjectID, objectType model.ObjectType) error {
	update := bson.M{
		"$pull": bson.M{
			"object_type_list": objectType,
		},
	}
	_, err := s.db.UpdateOne(ctx, bson.M{"_id": uid}, update)
	if err != nil {
		return err
	}

	return nil
}

func (s userService) GetOrCreateUser(ctx context.Context, mobile, openID, unionID string) (model.User, error) {
	byMobile, err := s.GetByMobile(ctx, mobile)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return model.User{}, err
	}
	now := time.Now().UnixMilli()
	if openID != "" && openID != byMobile.OpenID {
		zap.S().Infof("覆盖openID，用户:%s,手机:%s,原openID:%s，新openID:%s", byMobile.ID.Hex(), mobile, byMobile.OpenID, openID)
		err = s.userDao.Update(ctx, bson.M{"open_id": openID}, bson.M{"$set": bson.M{
			"open_id":    "",
			"unionID":    "",
			"updated_at": now,
		}})
		if err != nil {
			return model.User{}, err
		}
		err = s.userDao.Update(ctx, bson.M{"_id": byMobile.ID}, bson.M{"$set": bson.M{
			"open_id":    openID,
			"union_id":   unionID,
			"updated_at": now,
		}})
		if err != nil {
			return model.User{}, err
		}
		byMobile.UnionID = unionID
		byMobile.OpenID = openID
		byMobile.UpdatedAt = now
	}

	if byMobile.ID != primitive.NilObjectID {
		return byMobile, nil
	}
	//	创建

	initPWD := mobile[5:]

	en, err := s.AesSvr.En(initPWD)
	if err != nil {
		return model.User{}, err
	}

	u := model.User{
		ID:             primitive.NewObjectID(),
		Mobile:         mobile,
		OpenID:         openID,
		Password:       en,
		ObjectTypeList: []model.ObjectType{},
		CreatedAt:      now,
	}
	err = s.userDao.Create(ctx, u)
	if err != nil {
		return model.User{}, err
	}

	return u, nil
}

func (s userService) GetByMobile(ctx context.Context, mobile string) (model.User, error) {
	filter := bson.M{
		"mobile": mobile,
	}
	user, err := s.userDao.Get(ctx, filter)
	if err != nil {
		return model.User{}, err
	}
	return user, nil
}

func (s userService) GetByOpenID(ctx context.Context, env model.ObjectType, openID string) (model.User, error) {
	// env默认1 采购商
	filter := bson.M{
		"open_id": openID,
	}
	user, err := s.userDao.Get(ctx, filter)
	if err != nil {
		return model.User{}, err
	}
	return user, nil
}

func (s userService) LoginByMobile(ctx context.Context, mobile, captcha, loginCode string) (model.User, error) {
	err := messageService.NewMessageService().Check(mobile, captcha)
	if err != nil {
		return model.User{}, err
	}

	var openID string
	var unionID string
	if loginCode != "" {
		openID, unionID, _, err = s.mini.GetOpenID(loginCode)
		if err != nil {
			return model.User{}, err
		}
	}

	var u model.User
	u, err = s.GetOrCreateUser(ctx, mobile, openID, unionID)
	if err != nil {
		return model.User{}, err
	}

	return u, nil
}

func (s userService) LoginByPWD(ctx context.Context, mobile, pwd string) (model.User, error) {
	byMobile, err := s.GetByMobile(ctx, mobile)
	if err != nil {
		return model.User{}, err
	}

	if pwd == "" {
		return model.User{}, xerr.NewErr(xerr.ErrParamError, nil, "请输入密码")
	}

	en, err := s.AesSvr.En(pwd)
	if err != nil {
		return model.User{}, err
	}

	if byMobile.Password != en {
		return model.User{}, xerr.NewErr(xerr.ErrParamError, nil, "密码错误")
	}

	return byMobile, nil
}

func (s userService) InitPWD(ctx context.Context, mobile string) error {
	byMobile, err := s.GetByMobile(ctx, mobile)
	if err != nil {
		return err
	}

	initPWD := mobile[5:]

	en, err := s.AesSvr.En(initPWD)
	if err != nil {
		return err
	}

	err = s.userDao.Update(ctx, bson.M{
		"_id": byMobile.ID,
	}, bson.M{
		"$set": bson.M{
			"password": en,
		},
	})
	if err != nil {
		return err
	}

	return nil
}

func (s userService) ResetPWD(ctx context.Context, mobile, captcha, pwd string) error {
	byMobile, err := s.GetByMobile(ctx, mobile)
	if err != nil {
		return err
	}

	if pwd == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "请输入密码")
	}

	en, err := s.AesSvr.En(pwd)
	if err != nil {
		return err
	}

	err = s.userDao.Update(ctx, bson.M{
		"_id": byMobile.ID,
	}, bson.M{
		"$set": bson.M{
			"password": en,
		},
	})
	if err != nil {
		return err
	}

	return nil
}

func (s userService) CreateUser(ctx context.Context, mobile, pwd string) (model.User, error) {
	if pwd == "" {
		return model.User{}, xerr.NewErr(xerr.ErrParamError, nil, "请输入密码")
	}

	// 检查用户是否已存在
	byMobile, err := s.GetByMobile(ctx, mobile)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return model.User{}, err
	}
	if byMobile.ID != primitive.NilObjectID {
		return model.User{}, xerr.NewErr(xerr.ErrParamError, nil, "用户已存在")
	}

	// 加密密码
	en, err := s.AesSvr.En(pwd)
	if err != nil {
		return model.User{}, err
	}

	now := time.Now().UnixMilli()

	// 创建新用户
	u := model.User{
		ID:             primitive.NewObjectID(),
		Mobile:         mobile,
		Password:       en,
		ObjectTypeList: []model.ObjectType{},
		CreatedAt:      now,
		UpdatedAt:      now,
	}
	err = s.userDao.Create(ctx, u)
	if err != nil {
		return model.User{}, err
	}

	return u, nil
}

//
//func (s userService) LoginPre(ctx context.Context, env model.ObjectType, code string) (model.User, error) {
//	openID, unionID, _, err := s.mini.GetOpenID(code)
//	if err != nil {
//		return model.User{}, err
//	}
//	user, err := s.GetByOpenID(ctx, env, openID)
//	if err != nil {
//		return model.User{}, err
//	}
//
//	if user.UnionID == "" && unionID != "" {
//		_, err := s.db.UpdateOne(ctx, bson.M{"_id": user.ID}, bson.M{
//			"$set": bson.M{
//				"union_id": unionID,
//			},
//		})
//		if err != nil {
//			return model.User{}, err
//		}
//	}
//
//	return user, nil
//}

func (s userService) Login(ctx context.Context, loginCode, mobileCode string) (model.User, error) {
	openID, unionID, _, err := s.mini.GetOpenID(loginCode)
	if err != nil {
		return model.User{}, err
	}

	mobile, err := s.mini.GetMobile(mobileCode)
	if err != nil {
		return model.User{}, err
	}

	u, err := s.GetOrCreateUser(ctx, mobile, openID, unionID)
	if err != nil {
		return model.User{}, err
	}

	return u, nil
}

func (s userService) Get(ctx context.Context, id primitive.ObjectID) (model.User, error) {
	filter := bson.M{
		"_id": id,
	}
	user, err := s.userDao.Get(ctx, filter)
	if err != nil {
		return model.User{}, err
	}
	return user, nil
}

func (s userService) UpdateMobile(ctx context.Context, id primitive.ObjectID, mobile string) error {
	now := time.Now().UnixMilli()

	update := bson.M{
		"mobile":     mobile,
		"updated_at": now,
	}
	_, err := s.db.UpdateOne(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	return nil
}
