# 用户管理 API 文档

## 概述

本文档描述了用户管理相关的 API 接口，包括创建用户和重置密码功能。系统支持两种用户类型：普通用户（User）和采购商用户（Buyer）。

## 接口列表

### 1. 创建普通用户

**接口地址：** `POST /user/create`

**接口描述：** 创建新的普通用户账户

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数：**
```json
{
  "mobile": "***********",
  "pwd": "123456"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mobile | string | 是 | 手机号，11位数字 |
| pwd | string | 是 | 密码，不能为空 |

**响应示例：**

成功响应：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "64f1a2b3c4d5e6f7a8b9c0d1",
    "mobile": "***********",
    "open_id": "",
    "union_id": "",
    "note": "",
    "password": "encrypted_password",
    "object_type_list": [],
    "created_at": *************,
    "updated_at": *************
  }
}
```

失败响应：
```json
{
  "code": 400,
  "message": "用户已存在"
}
```

**错误码说明：**
- `400` - 参数错误（手机号格式错误、密码为空、用户已存在）
- `401` - 未授权访问
- `500` - 服务器内部错误

---

### 2. 创建采购商用户

**接口地址：** `POST /buyer/create`

**接口描述：** 创建新的采购商用户账户

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数：**
```json
{
  "mobile": "***********",
  "pwd": "123456"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mobile | string | 是 | 手机号，11位数字 |
| pwd | string | 是 | 密码，不能为空 |

**响应示例：**

成功响应：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "64f1a2b3c4d5e6f7a8b9c0d1",
    "buyer_name": "会员***********",
    "mobile": "***********",
    "open_id": "",
    "union_id": "",
    "password": "encrypted_password",
    "audit_status": 2,
    "account_status": 1,
    "user_type": 1,
    "service_fee_type": 1,
    "created_at": *************,
    "updated_at": *************
  }
}
```

失败响应：
```json
{
  "code": 400,
  "message": "用户已存在"
}
```

**错误码说明：**
- `400` - 参数错误（手机号格式错误、密码为空、用户已存在）
- `401` - 未授权访问
- `500` - 服务器内部错误

---

### 3. 重置普通用户密码

**接口地址：** `POST /user/pwd/reset`

**接口描述：** 重置已存在的普通用户密码

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数：**
```json
{
  "mobile": "***********",
  "pwd": "newpassword123"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mobile | string | 是 | 手机号，11位数字 |
| pwd | string | 是 | 新密码，不能为空 |

**响应示例：**

成功响应：
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

失败响应：
```json
{
  "code": 400,
  "message": "用户不存在"
}
```

**错误码说明：**
- `400` - 参数错误（手机号格式错误、密码为空、用户不存在）
- `401` - 未授权访问
- `500` - 服务器内部错误

---

### 4. 重置采购商用户密码

**接口地址：** `POST /buyer/pwd/reset`

**接口描述：** 重置已存在的采购商用户密码

**请求头：**
```
Content-Type: application/json
Authorization: Bearer {token}
```

**请求参数：**
```json
{
  "mobile": "***********",
  "pwd": "newpassword123"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| mobile | string | 是 | 手机号，11位数字 |
| pwd | string | 是 | 新密码，不能为空 |

**响应示例：**

成功响应：
```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

失败响应：
```json
{
  "code": 400,
  "message": "用户不存在"
}
```

**错误码说明：**
- `400` - 参数错误（手机号格式错误、密码为空、用户不存在）
- `401` - 未授权访问
- `500` - 服务器内部错误

## 注意事项

1. **权限要求：** 所有接口都需要在请求头中携带有效的 Authorization token
2. **手机号格式：** 必须是11位数字的有效手机号
3. **密码安全：** 密码在存储时会进行 AES 加密处理
4. **用户唯一性：** 同一手机号在同一用户类型中只能存在一个账户
5. **创建与重置的区别：**
   - 创建接口：仅用于创建新用户，如果用户已存在会返回错误
   - 重置接口：仅用于重置已存在用户的密码，如果用户不存在会返回错误

## 使用示例

### 创建用户示例

```bash
# 创建普通用户
curl -X POST "http://localhost:8080/user/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token_here" \
  -d '{
    "mobile": "***********",
    "pwd": "123456"
  }'

# 创建采购商用户
curl -X POST "http://localhost:8080/buyer/create" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token_here" \
  -d '{
    "mobile": "13800138001",
    "pwd": "123456"
  }'
```

### 重置密码示例

```bash
# 重置普通用户密码
curl -X POST "http://localhost:8080/user/pwd/reset" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token_here" \
  -d '{
    "mobile": "***********",
    "pwd": "newpassword123"
  }'

# 重置采购商用户密码
curl -X POST "http://localhost:8080/buyer/pwd/reset" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token_here" \
  -d '{
    "mobile": "13800138001",
    "pwd": "newpassword123"
  }'
```

## 更新日志

- **v1.0.0** (2024-01-24)
  - 新增创建用户接口
  - 新增重置密码接口
  - 支持普通用户和采购商用户两种类型
  - 移除验证码验证，简化密码管理流程
