package orderStockUp

import (
	"base/core/xhttp"
	"base/model"
	"base/service/orderQualityService"
	"base/service/orderService"
	"base/service/orderStockUpService"
	"base/util"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TabBarStats(ctx *gin.Context) {
	var req = struct {
		ServicePointID   string `json:"service_point_id"`
		Timestamp        int64  `json:"timestamp" validate:"-"`
		QualityTimestamp int64  `json:"quality_timestamp" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	countNotConfirm, err := orderStockUpService.NewOrderStockUpService().CountNotConfirm(ctx, servicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	var r WareHouseTabBar

	r.ToConfirmNum = int(countNotConfirm)

	ts, err := util.DayStartTimestamp(req.Timestamp)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	qCount, err := orderQualityService.NewOrderQualityService().CountNotQuality(ctx, ts, servicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	r.ToQualityNum = int(qCount)

	qSortCount, err := orderQualityService.NewOrderQualityService().CountNotSort(ctx, ts, servicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}
	r.ToSortNum = int(qSortCount)

	qualities, err := orderQualityService.NewOrderQualityService().ListQualityAll(ctx, ts, servicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	mToShipBuyerNum := make(map[primitive.ObjectID]int)
	for _, quality := range qualities {
		for _, v := range quality.OrderList {
			if !v.HasShip {
				// 未发货订单
				mToShipBuyerNum[v.BuyerID]++
			}
		}
	}

	r.ToShipBuyerNum = len(mToShipBuyerNum)

	xhttp.RespSuccess(ctx, r)
}

// CountNotConfirm 待确认订单
func CountNotConfirm(ctx *gin.Context) {
	var req = struct {
		ServicePointID   string `json:"service_point_id"`
		Timestamp        int64  `json:"timestamp" validate:"-"`
		QualityTimestamp int64  `json:"quality_timestamp" validate:"-"`
	}{}
	err := xhttp.Parse(ctx, &req)
	if err != nil {
		return
	}

	servicePointID, err := util.ConvertToObjectWithCtx(ctx, req.ServicePointID)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	filter := bson.M{
		"service_point_id": servicePointID,
		"pay_status":       model.PayStatusTypePaid,        // 已支付
		"order_status":     model.OrderStatusTypeToStockUp, // 待备货
		"order_type":       model.OrderTypeWholeSale,
		"user_type":        model.UserTypeNormal,
	}
	count, err := orderService.NewOrderService().Count(ctx, filter)
	if err != nil {
		xhttp.RespErr(ctx, err)
		return
	}

	xhttp.RespSuccess(ctx, count)
}

type WareHouseTabBar struct {
	ToConfirmNum   int `json:"to_confirm_num"`    // 待准备订单
	ToQualityNum   int `json:"to_quality_num"`    // 未品控单品
	ToSortNum      int `json:"to_sort_num"`       // 未分拣单品
	ToShipBuyerNum int `json:"to_ship_buyer_num"` // 未发货采购商数
}

// ToShipNum      int `json:"to_ship_num"`       // 未发货订单
