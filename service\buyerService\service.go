package buyerService

import (
	"base/core/xerr"
	"base/dao/buyerDao"
	"base/global"
	"base/model"
	"base/payModule"
	"base/service/aesService"
	"base/service/messageService"
	"base/service/miniService"
	"base/service/userAddrService"
	"base/service/userService"
	"base/types"
	"base/util"
	"context"
	"errors"
	"time"

	_ "github.com/alibabacloud-go/ecs-20140526/v2/client"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

type ServiceInterface interface {
	LoginByQuick(ctx context.Context, loginCode, mobileCode string) (model.Buyer, error)
	LoginByMobile(ctx context.Context, mobile, captcha, loginCode string) (model.Buyer, error)
	LoginByPWD(ctx context.Context, mobile, pwd, loginCode string) (model.Buyer, error)
	ResetPWD(ctx context.Context, mobile, captcha, pwd string) error
	CreateBuyer(ctx context.Context, mobile, pwd string) (model.Buyer, error)

	// Create(ctx context.Context, data model.Buyer) error
	// CheckBuyerInit(ctx context.Context, userID primitive.ObjectID) error
	UpdateAll(ctx context.Context, data model.Buyer) error
	UpdateBuyer(ctx context.Context, id primitive.ObjectID, buyerName string) error
	UpdateOne(ctx context.Context, id primitive.ObjectID, update bson.M) error
	UpdateLicenseStatus(ctx context.Context, id primitive.ObjectID, creditCode string) error
	UpdateAvatarImg(ctx context.Context, id primitive.ObjectID, f string) error
	UpdateInvoiceStatus(ctx context.Context, id primitive.ObjectID, status int) error
	UpdateAccountStatus(ctx context.Context, id primitive.ObjectID, status model.AccountStatusType) error
	UpdateServiceFeeType(ctx context.Context, id primitive.ObjectID, t model.ServiceFeeType) error
	UpdateUserType(ctx context.Context, id primitive.ObjectID, t model.UserType) error
	UpdateMany(ctx context.Context, ids []primitive.ObjectID, filter, update bson.M) error
	UpdateActiveExpire(ctx context.Context, buyerID primitive.ObjectID) error
	Audit(ctx context.Context, info model.Buyer, point model.ServicePoint, req types.BuyerAuditReq) error
	AssignPoint(ctx context.Context, info model.Buyer, point model.ServicePoint, station model.Station) error
	List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Buyer, int64, error)
	Count(ctx context.Context, filter bson.M) (int64, error)
	ListByCus(ctx context.Context, filter bson.M) ([]model.Buyer, error)
	ListByIDList(ctx context.Context, idList []primitive.ObjectID) ([]model.Buyer, error)
	GetByName(buyerName string) (model.Buyer, error)
	GetByMobile(ctx context.Context, mobile string) (model.Buyer, error)
	GetByNamePrecise(buyerName string) (model.Buyer, error)
	CheckExist(userID primitive.ObjectID, buyerName string) error
	Get(id primitive.ObjectID) (model.Buyer, error)
	GetByID(ctx context.Context, id primitive.ObjectID) (model.Buyer, error)
	GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.Buyer, error)
	CheckStatusByUserID(userID primitive.ObjectID) error
}

type buyerService struct {
	mdb      *mongo.Database
	rdb      *redis.Client
	buyerDao buyerDao.BuyerDao
	msg      messageService.ServiceInterface
	UserS    userService.ServiceInterface
	mini     miniService.ServiceInterface
	AesSvr   aesService.ServiceInterface

	AddrS     userAddrService.ServiceInterface
	AllInPayS payModule.MemberService
}

func NewBuyerService() ServiceInterface {
	return buyerService{
		mdb:      global.MDB,
		rdb:      global.RDBDefault,
		buyerDao: buyerDao.NewBuyerDao(),
		msg:      messageService.NewMessageService(),
		UserS:    userService.NewUserService(),

		mini:   miniService.NewMiniService(),
		AesSvr: aesService.NewAesService(),

		AddrS:     userAddrService.NewUserAddrService(),
		AllInPayS: payModule.NewMember(),
	}
}

func (s buyerService) LoginByQuick(ctx context.Context, loginCode, mobileCode string) (model.Buyer, error) {
	openID, unionID, _, err := s.mini.GetOpenID(loginCode)
	if err != nil {
		return model.Buyer{}, err
	}

	mobile, err := s.mini.GetMobile(mobileCode)
	if err != nil {
		return model.Buyer{}, err
	}

	u, err := s.GetOrCreate(ctx, mobile, openID, unionID)
	if err != nil {
		return model.Buyer{}, err
	}

	return u, nil
}

func (s buyerService) LoginByMobile(ctx context.Context, mobile, captcha, loginCode string) (model.Buyer, error) {
	err := messageService.NewMessageService().CheckBuyerLogin(mobile, captcha)
	if err != nil {
		return model.Buyer{}, err
	}

	var openID string
	var unionID string
	if loginCode != "" {
		openID, unionID, _, err = s.mini.GetOpenID(loginCode)
		if err != nil {
			return model.Buyer{}, err
		}
	}

	var u model.Buyer
	u, err = s.GetOrCreate(ctx, mobile, openID, unionID)
	if err != nil {
		return model.Buyer{}, err
	}

	return u, nil
}

func (s buyerService) GetOrCreate(ctx context.Context, mobile, openID, unionID string) (model.Buyer, error) {
	byMobile, err := s.GetByMobile(ctx, mobile)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return model.Buyer{}, err
	}
	now := time.Now().UnixMilli()
	if openID != "" && openID != byMobile.OpenID {
		zap.S().Infof("覆盖openID，用户:%s,手机:%s,原openID:%s，新openID:%s", byMobile.ID.Hex(), mobile, byMobile.OpenID, openID)
		err = s.buyerDao.Update(ctx, bson.M{"open_id": openID}, bson.M{"$set": bson.M{
			"open_id":    "",
			"unionID":    "",
			"updated_at": now,
		}})
		if err != nil {
			return model.Buyer{}, err
		}
		err = s.buyerDao.Update(ctx, bson.M{"_id": byMobile.ID}, bson.M{"$set": bson.M{
			"open_id":    openID,
			"union_id":   unionID,
			"updated_at": now,
		}})
		if err != nil {
			return model.Buyer{}, err
		}
		byMobile.UnionID = unionID
		byMobile.OpenID = openID
		byMobile.UpdatedAt = now

		del(s.rdb, byMobile.ID)
	}

	if byMobile.ID != primitive.NilObjectID {
		return byMobile, nil
	}
	//	创建
	u := model.Buyer{
		ID:             primitive.NewObjectID(),
		BuyerName:      "会员" + mobile,
		Mobile:         mobile,
		OpenID:         openID,
		AuditStatus:    model.AuditStatusTypePass,
		AccountStatus:  model.AccountStatusTypeNormal,
		UserType:       model.UserTypeNormal,
		ServiceFeeType: model.ServiceFeeTypeOne,
		CreatedAt:      now,
		UpdatedAt:      now,
	}
	err = s.buyerDao.Create(ctx, u)
	if err != nil {
		return model.Buyer{}, err
	}

	return u, nil
}

func (s buyerService) GetByMobile(ctx context.Context, mobile string) (model.Buyer, error) {
	filter := bson.M{
		"mobile": mobile,
	}
	user, err := s.buyerDao.Get(ctx, filter)
	if err != nil {
		return model.Buyer{}, err
	}
	return user, nil
}

func (s buyerService) LoginByPWD(ctx context.Context, mobile, pwd, loginCode string) (model.Buyer, error) {
	byMobile, err := s.GetByMobile(ctx, mobile)
	if err != nil {
		return model.Buyer{}, err
	}

	if pwd == "" {
		return model.Buyer{}, xerr.NewErr(xerr.ErrParamError, nil, "请输入密码")
	}

	en, err := s.AesSvr.En(pwd)
	if err != nil {
		return model.Buyer{}, err
	}

	if byMobile.Password != en {
		return model.Buyer{}, xerr.NewErr(xerr.ErrParamError, nil, "密码错误")
	}

	var openID string
	var unionID string
	if loginCode != "" {
		openID, unionID, _, err = s.mini.GetOpenID(loginCode)
		if err != nil {
			return model.Buyer{}, err
		}
	}

	byMobile, err = s.GetOrCreate(ctx, mobile, openID, unionID)
	if err != nil {
		return model.Buyer{}, err
	}

	return byMobile, nil
}

func (s buyerService) InitPWD(ctx context.Context, mobile string) error {
	byMobile, err := s.GetByMobile(ctx, mobile)
	if err != nil {
		return err
	}

	initPWD := mobile[5:]

	en, err := s.AesSvr.En(initPWD)
	if err != nil {
		return err
	}

	err = s.buyerDao.Update(ctx, bson.M{
		"_id": byMobile.ID,
	}, bson.M{
		"$set": bson.M{
			"password": en,
		},
	})
	if err != nil {
		return err
	}

	del(s.rdb, byMobile.ID)

	return nil
}

func (s buyerService) ResetPWD(ctx context.Context, mobile, captcha, pwd string) error {
	byMobile, err := s.GetByMobile(ctx, mobile)
	if err != nil {
		return err
	}

	if pwd == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "请输入密码")
	}

	en, err := s.AesSvr.En(pwd)
	if err != nil {
		return err
	}

	err = s.buyerDao.Update(ctx, bson.M{
		"_id": byMobile.ID,
	}, bson.M{
		"$set": bson.M{
			"password": en,
		},
	})
	if err != nil {
		return err
	}

	del(s.rdb, byMobile.ID)

	return nil
}

func (s buyerService) CreateBuyer(ctx context.Context, mobile, pwd string) (model.Buyer, error) {
	if pwd == "" {
		return model.Buyer{}, xerr.NewErr(xerr.ErrParamError, nil, "请输入密码")
	}

	// 检查用户是否已存在
	byMobile, err := s.GetByMobile(ctx, mobile)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return model.Buyer{}, err
	}
	if byMobile.ID != primitive.NilObjectID {
		return model.Buyer{}, xerr.NewErr(xerr.ErrParamError, nil, "用户已存在")
	}

	// 加密密码
	en, err := s.AesSvr.En(pwd)
	if err != nil {
		return model.Buyer{}, err
	}

	now := time.Now().UnixMilli()

	// 创建新用户
	u := model.Buyer{
		ID:             primitive.NewObjectID(),
		BuyerName:      "会员" + mobile,
		Mobile:         mobile,
		Password:       en,
		AuditStatus:    model.AuditStatusTypePass,
		AccountStatus:  model.AccountStatusTypeNormal,
		UserType:       model.UserTypeNormal,
		ServiceFeeType: model.ServiceFeeTypeOne,
		CreatedAt:      now,
		UpdatedAt:      now,
	}
	err = s.buyerDao.Create(ctx, u)
	if err != nil {
		return model.Buyer{}, err
	}

	return u, nil
}

func (s buyerService) GetByUserID(ctx context.Context, userID primitive.ObjectID) (model.Buyer, error) {
	m := getByUser(s.rdb, userID)
	if m.ID == primitive.NilObjectID {
		buyer, err := s.buyerDao.Get(ctx, bson.M{"user_id": userID})
		if err != nil {
			return model.Buyer{}, err
		}
		setByUser(s.rdb, buyer)

		return buyer, nil
	}
	return m, nil
}

func (s buyerService) CheckStatusByUserID(userID primitive.ObjectID) error {
	byUser, err := s.GetByUserID(context.Background(), userID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	//if byUser.AuditStatus != model.AuditStatusTypePass {
	//	return xerr.NewErr(xerr.ErrParamError, nil, "请在审核通过后再进行操作")
	//}
	if byUser.AccountStatus != model.AccountStatusTypeNormal {
		return xerr.NewErr(xerr.ErrParamError, nil, "账号异常，请联系管理员")
	}
	return nil
}

func (s buyerService) CheckExist(userID primitive.ObjectID, buyerName string) error {
	buyer, err := s.GetByUserID(context.Background(), userID)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if buyer.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "该用户已经申请采购商，请勿重复申请")
	}

	getByName, err := s.GetByName(buyerName)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if getByName.ID != primitive.NilObjectID {
		return xerr.NewErr(xerr.ErrParamError, nil, "提交失败，名称已存在")
	}
	return nil
}

func (s buyerService) GetByName(buyerName string) (model.Buyer, error) {
	buyer, err := s.buyerDao.Get(context.Background(), bson.M{
		"buyer_name": bson.M{"$regex": buyerName},
	})
	if err != nil {
		return model.Buyer{}, err
	}
	return buyer, nil
}

// GetByNamePrecise 精确查询
func (s buyerService) GetByNamePrecise(buyerName string) (model.Buyer, error) {
	buyer, err := s.buyerDao.Get(context.Background(), bson.M{
		"buyer_name": buyerName,
	})
	if err != nil {
		return model.Buyer{}, err
	}
	return buyer, nil
}

func (s buyerService) List(ctx context.Context, filter bson.M, page, limit int64) ([]model.Buyer, int64, error) {
	list, i, err := s.buyerDao.List(ctx, filter, page, limit)
	if err != nil {
		return nil, 0, err
	}
	return list, i, nil
}
func (s buyerService) Count(ctx context.Context, filter bson.M) (int64, error) {
	i, err := s.buyerDao.Count(context.Background(), filter)
	if err != nil {
		return 0, err
	}
	return i, nil
}

func (s buyerService) ListByCus(ctx context.Context, filter bson.M) ([]model.Buyer, error) {
	list, err := s.buyerDao.ListByCus(context.Background(), filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s buyerService) ListByIDList(ctx context.Context, idList []primitive.ObjectID) ([]model.Buyer, error) {
	if len(idList) == 0 {
		return nil, nil
	}
	filter := bson.M{
		"_id": bson.M{
			"$in": idList,
		},
	}
	list, err := s.buyerDao.ListByCus(context.Background(), filter)
	if err != nil {
		return nil, err
	}
	return list, nil
}

func (s buyerService) Get(id primitive.ObjectID) (model.Buyer, error) {
	m := get(s.rdb, id)
	if m.ID == primitive.NilObjectID {
		warehouse, err := s.buyerDao.Get(context.Background(), bson.M{"_id": id})
		if err != nil {
			return model.Buyer{}, err
		}
		set(s.rdb, warehouse)
		return warehouse, nil
	}
	return m, nil
}

func (s buyerService) GetByID(ctx context.Context, id primitive.ObjectID) (model.Buyer, error) {
	m := get(s.rdb, id)
	if m.ID == primitive.NilObjectID {
		warehouse, err := s.buyerDao.Get(ctx, bson.M{"_id": id})
		if err != nil {
			return model.Buyer{}, err
		}
		set(s.rdb, warehouse)
		return warehouse, nil
	}
	return m, nil
}

func (s buyerService) Audit(ctx context.Context, info model.Buyer, point model.ServicePoint, req types.BuyerAuditReq) error {
	milli := time.Now().UnixMilli()
	update := bson.M{
		"audit_status": req.AuditStatus,
		"updated_at":   milli,
	}

	if req.AuditStatus == model.AuditStatusTypeNotPass {
		update["audit_fail_reason"] = req.AuditFailReason
	}

	if req.AuditStatus == model.AuditStatusTypePass {
		update["note"] = req.BuyerNote
		//update["service_point_id"] = point.ID
		//update["service_point_name"] = point.Name
		update["deliver_type"] = req.DeliverType
		update["deliver_fee"] = req.DeliverFee
		//update["subsidy_amount"] = req.SubsidyAmount
		//update["subsidy_percent"] = req.SubsidyPercent
		update["address_note"] = req.AddressNote
		update["deliver_free_begin"] = req.DeliverFreeBegin
		update["deliver_free_end"] = req.DeliverFreeEnd
		update["audit_fail_reason"] = ""

		update["logistics_note"] = req.LogisticsNote
		update["logistics_unit_fee"] = req.LogisticsUnitFee

		update["instant_deliver"] = req.InstantDeliver

		//update["price_level"] = req.PriceLevel
		update["service_fee"] = req.ServiceFee
		update["service_fee_rebate_percent"] = req.ServiceFeeRebatePercent
		update["entity"] = req.Entity
		update["user_type"] = req.UserType
	}

	err := s.buyerDao.Update(ctx, bson.M{"_id": info.ID}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	del(s.rdb, info.ID)
	delByUser(s.rdb, info.UserID)

	user, err := s.UserS.Get(context.Background(), info.UserID)
	if err != nil {
		return err
	}

	msg := "已通过"
	if req.AuditStatus == model.AuditStatusTypeNotPass {
		msg = "暂时不能为您服务"
	}
	err = s.msg.SendAuditInfo(user.Mobile, "果蔬团会员", msg)

	if err != nil {
		zap.S().Errorf("数据更新后，短信通知失败,%s", err.Error())
		return err
	}

	if req.AuditStatus == model.AuditStatusTypePass {

		info, err = s.Get(info.ID)
		//	 通过，创建地址
		err = s.AddrS.InitByBuyer(ctx, info)
		if err != nil {
			zap.S().Errorf("InitByBuyer失败,%s", err.Error())
			return err
		}
	}

	return nil
}

func (s buyerService) AssignPoint(ctx context.Context, info model.Buyer, point model.ServicePoint, station model.Station) error {
	milli := time.Now().UnixMilli()
	update := bson.M{
		"is_assign_service_point": true,
		"service_point_id":        point.ID,
		"service_point_name":      point.Name,
		"station_id":              station.ID,
		"station_name":            station.Name,
		"updated_at":              milli,
	}

	err := s.buyerDao.Update(ctx, bson.M{"_id": info.ID}, bson.M{"$set": update})
	if err != nil {
		return err
	}

	del(s.rdb, info.ID)
	delByUser(s.rdb, info.UserID)

	return nil
}

func (s buyerService) UpdateActiveExpire(ctx context.Context, id primitive.ObjectID) error {
	buyer, err := s.Get(id)
	if err != nil {
		return err
	}
	update := bson.M{}
	update["active_expire"] = util.ExpireBuyerTime()
	err = s.buyerDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	del(s.rdb, id)
	delByUser(s.rdb, buyer.UserID)
	return nil
}

func (s buyerService) UpdateLicenseStatus(ctx context.Context, id primitive.ObjectID, creditCode string) error {
	buyer, err := s.Get(id)
	if err != nil {
		return err
	}

	now := time.Now().UnixMilli()

	status := 1
	if creditCode != "" {
		status = 2
	}

	update := bson.M{
		"license_status": status,
		"credit_code":    creditCode,
		"updated_at":     now,
	}
	err = s.buyerDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	del(s.rdb, id)
	delByUser(s.rdb, buyer.UserID)
	return nil
}

func (s buyerService) UpdateServiceFeeType(ctx context.Context, id primitive.ObjectID, t model.ServiceFeeType) error {
	buyer, err := s.Get(id)
	if err != nil {
		return err
	}

	now := time.Now().UnixMilli()

	update := bson.M{
		"service_fee_type": t,
		"updated_at":       now,
	}

	if buyer.UserType == model.UserTypeYHT && t != model.ServiceFeeTypeNone {
		return xerr.NewErr(xerr.ErrParamError, nil, "益禾堂客户不收取服务费")
	}

	err = s.buyerDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	del(s.rdb, id)
	delByUser(s.rdb, buyer.UserID)
	return nil
}

func (s buyerService) UpdateUserType(ctx context.Context, id primitive.ObjectID, t model.UserType) error {
	buyer, err := s.Get(id)
	if err != nil {
		return err
	}

	now := time.Now().UnixMilli()

	update := bson.M{
		"user_type":  t,
		"updated_at": now,
	}

	if t == model.UserTypeYHT {
		update["service_fee_type"] = model.ServiceFeeTypeNone
	}

	err = s.buyerDao.Update(ctx, bson.M{"_id": id}, bson.M{"$set": update})
	if err != nil {
		return err
	}
	del(s.rdb, id)
	delByUser(s.rdb, buyer.UserID)
	return nil
}

func (s buyerService) UpdateMany(ctx context.Context, ids []primitive.ObjectID, filter, update bson.M) error {
	err := s.buyerDao.UpdateMany(ctx, filter, update)
	if err != nil {
		return err
	}
	for _, id := range ids {
		buyer, err := s.Get(id)
		if err != nil {
			return err
		}
		del(s.rdb, id)
		delByUser(s.rdb, buyer.UserID)
	}
	return nil
}

func (s buyerService) UpdateBuyer(ctx context.Context, id primitive.ObjectID, buyerName string) error {
	if buyerName == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "会员名称不能为空")
	}

	buyer, err := s.Get(id)
	if err != nil {
		return err
	}

	namePrecise, err := s.GetByNamePrecise(buyerName)
	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
		return err
	}
	if !errors.Is(err, mongo.ErrNoDocuments) && namePrecise.ID != id {
		return xerr.NewErr(xerr.ErrParamError, nil, "该会员名称已被使用")
	}

	now := time.Now().UnixMilli()
	filter := bson.M{"_id": id}
	update := bson.M{
		"$set": bson.M{
			"buyer_name": buyerName,
			"updated_at": now,
		},
	}

	err = s.buyerDao.Update(ctx, filter, update)
	if err != nil {
		return err
	}
	del(s.rdb, id)
	delByUser(s.rdb, buyer.UserID)
	return nil
}

func (s buyerService) UpdateOne(ctx context.Context, id primitive.ObjectID, update bson.M) error {
	filter := bson.M{"_id": id}
	u := bson.M{
		"$set": update,
	}

	err := s.buyerDao.Update(ctx, filter, u)
	if err != nil {
		return err
	}
	del(s.rdb, id)
	return nil
}

func (s buyerService) UpdateAvatarImg(ctx context.Context, id primitive.ObjectID, f string) error {
	if f == "" {
		return xerr.NewErr(xerr.ErrParamError, nil, "图片地址不能为空")
	}

	buyer, err := s.Get(id)
	if err != nil {
		return err
	}

	now := time.Now().UnixMilli()
	filter := bson.M{"_id": id}
	update := bson.M{
		"$set": bson.M{
			"avatar_file": f,
			"updated_at":  now,
		},
	}

	err = s.buyerDao.Update(ctx, filter, update)
	if err != nil {
		return err
	}

	del(s.rdb, id)
	delByUser(s.rdb, buyer.UserID)

	return nil
}

func (s buyerService) UpdateInvoiceStatus(ctx context.Context, id primitive.ObjectID, status int) error {
	buyer, err := s.Get(id)
	if err != nil {
		return err
	}

	now := time.Now().UnixMilli()
	filter := bson.M{"_id": id}
	update := bson.M{
		"$set": bson.M{
			"invoice_auth_status": status,
			"updated_at":          now,
		},
	}

	err = s.buyerDao.Update(ctx, filter, update)
	if err != nil {
		return err
	}

	del(s.rdb, id)
	delByUser(s.rdb, buyer.UserID)

	return nil
}

func (s buyerService) UpdateAccountStatus(ctx context.Context, id primitive.ObjectID, status model.AccountStatusType) error {
	buyer, err := s.Get(id)
	if err != nil {
		return err
	}

	now := time.Now().UnixMilli()
	filter := bson.M{"_id": id}
	update := bson.M{
		"$set": bson.M{
			"account_status": status,
			"updated_at":     now,
		},
	}

	err = s.buyerDao.Update(ctx, filter, update)
	if err != nil {
		return err
	}

	del(s.rdb, id)
	delByUser(s.rdb, buyer.UserID)

	return nil
}

//
//func (s buyerService) Create(ctx context.Context, data model.Buyer) error {
//	_, err := s.GetByNamePrecise(data.BuyerName)
//	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
//		return err
//	}
//	if !errors.Is(err, mongo.ErrNoDocuments) {
//		zap.S().Warnf("申请会员，名称已被使用，名称：%s", data.BuyerName)
//		return xerr.NewErr(xerr.ErrParamError, nil, "该会员名称已被使用")
//	}
//
//	user, err := s.UserS.Get(ctx, data.UserID)
//	if err != nil {
//		return err
//	}
//
//	// 开启事务
//	session, err := s.mdb.Client().StartSession()
//	if err != nil {
//		return err
//	}
//	defer session.EndSession(ctx)
//
//	_, err = session.WithTransaction(ctx, func(sessCtx mongo.SessionContext) (interface{}, error) {
//
//		err = s.authenticationS.CreateIndividual(sessCtx, user.Mobile, data.UserID, data.ID, model.ObjectTypeBuyer, pays.MemberTypeIndividual)
//		if err != nil {
//			return nil, err
//		}
//
//		err = s.buyerDao.Create(sessCtx, data)
//		if err != nil {
//			return nil, err
//		}
//
//		return nil, nil
//	})
//	if err != nil {
//		return err
//	}
//
//	return nil
//}
//
//func (s buyerService) CheckBuyerInit(ctx context.Context, userID primitive.ObjectID) error {
//	user, err := s.UserS.Get(ctx, userID)
//	if err != nil {
//		return err
//	}
//
//	buyer, err := s.GetByUserID(ctx, userID)
//	if err != nil && !errors.Is(err, mongo.ErrNoDocuments) {
//		return err
//	}
//
//	if errors.Is(err, mongo.ErrNoDocuments) {
//		zap.S().Infof("初始化用户：%s", user.Mobile)
//		buyer.ID = primitive.NewObjectID()
//		buyer.UserID = userID
//		buyer.BuyerName = user.Mobile
//		buyer.BuyerType = model.ShopTypeOther
//		buyer.MemberType = pays.MemberTypeIndividual
//
//		buyer.Entity = 2
//
//		pointID, _ := util.ConvertToObjectWithCtx(ctx, "647d77ef1db1e622b23c3339")
//		buyer.ServicePointID = pointID
//
//		milli := time.Now().UnixMilli()
//		buyer.CreatedAt = milli
//		buyer.UpdatedAt = milli
//
//		buyer.AccountStatus = model.AccountStatusTypeNotApply
//		buyer.AuditStatus = model.AuditStatusTypeNone
//
//		// 新建
//		err = s.authenticationS.CreateIndividual(ctx, user.Mobile, userID, buyer.ID, model.ObjectTypeBuyer, pays.MemberTypeIndividual)
//		if err != nil {
//			return err
//		}
//
//		err = s.buyerDao.Create(ctx, buyer)
//		if err != nil {
//			return err
//		}
//	}
//
//	return nil
//}

func (s buyerService) UpdateAll(ctx context.Context, data model.Buyer) error {
	filter := bson.M{
		"_id": data.ID,
	}

	update := bson.M{
		"$set": data,
	}

	err := s.buyerDao.Update(ctx, filter, update)
	if err != nil {
		return err
	}

	del(s.rdb, data.ID)
	delByUser(s.rdb, data.UserID)

	err = s.AddrS.InitByBuyer(ctx, data)
	if err != nil {
		zap.S().Errorf("InitByBuyer失败,%s", err.Error())
		return err
	}

	return nil
}
